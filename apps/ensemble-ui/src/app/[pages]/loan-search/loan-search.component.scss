// Centered within content area
.loan-search-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; // Back to center for search form
  // Calculate available height: 100vh - header (48px + 32px padding) - page content padding (40px)
  min-height: calc(100vh - 120px);
  padding: 0; // Remove padding to use full content area
  margin: -20px; // Offset the page-content padding to use full area
  background: transparent;
  position: relative;
  transition: opacity 0.3s ease;



  &.searching {
    .app-title,
    .search-form,
    .recent-searches,
    .search-tips {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// Application title styling
.app-title {
  position: absolute;
  top: 8vh; // Position from top of container
  left: 50%;
  transform: translateX(-50%); // Center horizontally
  text-align: center;
  animation: titleFadeIn 1.2s ease-out;
  z-index: 1; // Ensure it's above other content

  h1 {
    font-size: 4rem;
    font-weight: 300;
    color: #202124;
    margin: 0;
    letter-spacing: -0.02em;
    animation: titleSlideUp 1s ease-out 0.3s both;

    // Add subtle text shadow for depth
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    // Individual letter styling
    .letter {
      display: inline-block;
      font-family: 'Courier New', monospace; // Monospace for consistent number/letter width
      transition: all 0.3s ease;
      color: #de3341; // Start with Rocket red for numbers

      &.settled {
        color: #202124; // Fade to normal text color when settled
        font-family: inherit; // Return to normal font
      }
    }

    // Responsive font sizing
    @media (max-width: 768px) {
      font-size: 3rem;
    }

    @media (max-width: 480px) {
      font-size: 2.5rem;
    }
  }
}

// Centered search form
.search-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  width: 100%;
  max-width: 600px;
  // Remove flex properties to let parent handle centering

  // Minimal underline search component
  .search-input-container {
    position: relative;
    width: 100%;
    max-width: 480px;
    background: transparent;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 16px;

    // Animated input styling
    ens-animated-loan-input {
      width: 100%;
      max-width: 500px;
      margin: 0 auto;
    }

    // Reserved space for messages
    .message-area {
      min-height: 80px; // Increased from 48px to 80px for more space
      display: flex;
      align-items: flex-start;
      justify-content: center;
      transition: all 0.3s ease;
      margin-top: 16px; // Add some top margin for better spacing
    }
  }

    .search-input {
      flex: 1;
      margin: 0;

      ::ng-deep {
        .mat-mdc-form-field-wrapper {
          background: transparent;
          padding: 0;
        }

        .mat-mdc-form-field-flex {
          padding: 0; // Remove padding that pushes input up
          align-items: center;
          height: 48px; // Reduce height to match buttons
        }

        .mat-mdc-form-field-outline {
          display: none; // Remove outline for underline style
        }

        .mat-mdc-form-field-outline-thick {
          display: none;
        }

        .mdc-text-field {
          background: transparent;
          border: none;
          padding: 0;
          border-bottom: 1px solid rgba(95, 99, 104, 0.3);
          border-radius: 0;
          transition: border-color 0.2s ease;
          height: 48px; // Match form field height
        }

        .mdc-text-field--focused {
          border-bottom-color: #1976d2;
        }

        .mat-mdc-form-field-hint-wrapper,
        .mat-mdc-form-field-error-wrapper {
          display: none; // Remove validation messages
        }

        .mat-mdc-floating-label {
          color: #5f6368;
          font-size: 16px;
        }

        .mdc-text-field--focused .mdc-floating-label {
          color: #1976d2;
        }

        input {
          font-size: 16px;
          color: #202124;
          caret-color: #1976d2;
          background: transparent;
          height: 24px; // Proper input height
          line-height: 24px;
        }
      }
    }


  }

  .search-error {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 1rem;
    padding: 12px 20px;
    background: rgba(244, 67, 54, 0.1);
    border-radius: 20px;
    color: #d32f2f;
    font-size: 14px;
    width: 100%;
    max-width: 480px;
    animation: slideIn 0.3s ease-out;

    mat-icon {
      color: #d32f2f;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }

  .rate-limit-warning {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #fff3e0;
    border-radius: 4px;
    color: #ef6c00;

    mat-icon {
      color: #ef6c00;
    }
  }

  // Loading message styling
  .search-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #1976d2;
    font-size: 14px;
    font-weight: 500;
    animation: fadeIn 0.3s ease-out;

    span {
      animation: pulse 1.5s ease-in-out infinite;
    }
  }

  // Centered recent searches
  .recent-searches {
  width: 100%;
  max-width: 480px;
  margin: 0 auto 2rem auto;

  .recent-searches-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 1rem;
    color: #5f6368;
    font-size: 14px;
    font-weight: 500;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: #5f6368;
    }

    .clear-all-button {
      margin-left: auto;
      border-radius: 16px;
      padding: 4px 12px;
      font-size: 12px;
      min-height: 28px;
      color: #5f6368;

      &:hover {
        background-color: rgba(95, 99, 104, 0.08);
      }
    }
  }

  .recent-search-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    margin: 4px auto;
    max-width: 300px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.05);
    color: #5f6368;
    font-size: 14px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #202124;
    }

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      color: inherit;
    }

    .loan-number {
      flex: 1;
      text-align: center;
    }

    .remove-button {
      border-radius: 50%;
      width: 24px;
      height: 24px;
      min-width: 24px;
      padding: 0;
      opacity: 0;
      transition: opacity 0.2s ease;

      mat-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
    }

    &:hover .remove-button {
      opacity: 1;
    }
  }
  // Centered search tips
  .search-tips {
  text-align: center;
  color: #5f6368;
  font-size: 13px;
  line-height: 1.4;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;

  .tip-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-bottom: 8px;
    color: #5f6368;
  }

  .tip-text {
    margin: 0;
  }
  }
}

// Responsive design
@media (max-width: 768px) {
  .loan-search-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .app-title {
    top: 4vh; // Reduce top position on mobile
  }

  .search-form {
    .search-input-container {
      max-width: 100%;
    }


  }

  .recent-searches {
    .recent-search-item {
      max-width: 100%;
    }
  }
}



// Smooth animations
.search-error {
  animation: slideIn 0.3s ease-out;
}

.recent-search-item {
  animation: fadeIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Pulse animation for rate limit warning
.rate-limit-warning {
  animation: slideIn 0.3s ease-out, pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

// Title animations
@keyframes titleFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes titleSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
