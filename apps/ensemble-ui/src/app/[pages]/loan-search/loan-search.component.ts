import { Component, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, debounceTime, distinctUntilChanged, catchError, of } from 'rxjs';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';


// Services and models
import { LoanService } from '../../shared/services/loan.service';
import { AnimatedLoanInputComponent } from '../../shared/components/animated-loan-input/animated-loan-input.component';

// Define the loan response interface locally for now
interface LoanResponseDto {
  id: string;
  loanNumber: string;
  borrowerName: string;
  borrowerEmail?: string;
  loanAmount: number;
  status: string;
  applicationDate: string;
  propertyAddress: string;
}

@Component({
  selector: 'ens-loan-search',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    MatListModule,
    MatDividerModule,
    AnimatedLoanInputComponent
  ],
  templateUrl: './loan-search.component.html',
  styleUrls: ['./loan-search.component.scss']
})
export class LoanSearchComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly router = inject(Router);
  private readonly loanService = inject(LoanService);

  // Form controls
  loanNumberControl = new FormControl('');

  // Component state
  isSearching = false;
  searchError: string | null = null;
  recentSearches: string[] = [];
  searchAttempts = 0;
  maxSearchAttempts = 5;

  // Experimental animated input
  animatedInputValue = '';

  ngOnInit(): void {
    this.loadRecentSearches();
    this.setupSearchDebounce();
  }



  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load recent searches from the service
   */
  private loadRecentSearches(): void {
    this.loanService.recentSearches$
      .pipe(takeUntil(this.destroy$))
      .subscribe(searches => {
        this.recentSearches = searches;
      });
  }

  /**
   * Setup debounced search for better UX
   */
  private setupSearchDebounce(): void {
    this.loanNumberControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        if (value && this.loanNumberControl.valid) {
          // Auto-search when valid loan number is entered
          // This is optional - you might want to require explicit search button click
        }
      });
  }

  /**
   * Perform loan search
   */
  search(): void {
    const loanNumber = this.loanNumberControl.value?.trim();
    if (!loanNumber) {
      return;
    }

    // Rate limiting check
    if (this.searchAttempts >= this.maxSearchAttempts) {
      this.searchError = 'Too many search attempts. Please wait a moment before trying again.';
      return;
    }

    this.isSearching = true;
    this.searchError = null;
    this.searchAttempts++;

    // Reset search attempts after 1 minute
    setTimeout(() => {
      this.searchAttempts = Math.max(0, this.searchAttempts - 1);
    }, 60000);

    this.loanService.findLoanByNumber(loanNumber)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          this.handleSearchError(error);
          return of(null);
        })
      )
      .subscribe({
        next: (loan: LoanResponseDto | null) => {
          this.isSearching = false;
          if (loan) {
            this.navigateToLoanDetails(loan);
          }
        }
      });
  }

  /**
   * Navigate to loan details page
   */
  private navigateToLoanDetails(loan: LoanResponseDto): void {
    this.router.navigate(['/loan-details', loan.id]);
  }

  /**
   * Handle search errors
   */
  private handleSearchError(error: any): void {
    this.isSearching = false;

    if (error.status === 404) {
      this.searchError = 'Loan not found. Please verify the loan number and try again.';
    } else if (error.status === 0) {
      this.searchError = 'Unable to connect to the server. Please check your internet connection and try again.';
    } else if (error.status === 429) {
      this.searchError = 'Too many requests. Please wait a moment before searching again.';
    } else if (error.status === 500) {
      this.searchError = 'Server error occurred. Please try again later or contact support.';
    } else if (error.status === 403) {
      this.searchError = 'Access denied. You may not have permission to view this loan.';
    } else {
      this.searchError = `An unexpected error occurred (${error.status || 'Unknown'}). Please try again.`;
    }

    // Log error for debugging (in production, this would go to a logging service)
    console.error('Loan search error:', error);
  }

  /**
   * Select a recent search
   */
  selectRecentSearch(loanNumber: string): void {
    this.loanNumberControl.setValue(loanNumber);
    this.search();
  }

  /**
   * Clear recent searches
   */
  clearRecentSearches(): void {
    this.loanService.clearRecentSearches();
  }

  /**
   * Clear the search input
   */
  clearSearch(): void {
    this.loanNumberControl.reset();
    this.searchError = null;
  }



  /**
   * Format loan number for display
   */
  formatLoanNumber(loanNumber: string): string {
    return this.loanService.formatLoanNumber(loanNumber);
  }

  /**
   * Handle Enter key press in search input
   */
  onEnterKey(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.search();
    }
  }

  /**
   * TrackBy function for recent searches list
   */
  trackByLoanNumber(_index: number, loanNumber: string): string {
    return loanNumber;
  }

  /**
   * Handle input changes
   */
  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/\D/g, ''); // Remove non-digits

    // Update the form control with clean value
    this.loanNumberControl.setValue(value, { emitEvent: false });
  }



  /**
   * Check if search button should be disabled
   */
  isSearchDisabled(): boolean {
    return this.isSearching ||
           this.searchAttempts >= this.maxSearchAttempts ||
           !this.loanNumberControl.value?.trim();
  }

  /**
   * Get remaining search attempts
   */
  getRemainingAttempts(): number {
    return Math.max(0, this.maxSearchAttempts - this.searchAttempts);
  }

  /**
   * Check if rate limit warning should be shown
   */
  shouldShowRateLimitWarning(): boolean {
    return this.searchAttempts >= this.maxSearchAttempts - 1;
  }

  /**
   * Remove a specific loan number from recent searches
   */
  removeFromRecent(loanNumber: string, event: Event): void {
    event.stopPropagation(); // Prevent triggering the search
    this.loanService.removeFromRecentSearches(loanNumber);
  }

  /**
   * Handle animated input value changes
   */
  onAnimatedInputChange(value: string): void {
    const previousLength = this.animatedInputValue.length;
    this.animatedInputValue = value;

    // Update the form control to maintain compatibility with existing logic
    this.loanNumberControl.setValue(value, { emitEvent: false });

    // Clear error message when user starts backspacing (reducing digits)
    if (value.length < previousLength && this.searchError) {
      this.searchError = null;
    }

    // Auto-navigate when 10 digits are entered
    if (value && value.length === 10) {
      this.autoNavigateToLoanDetails(value);
    }
  }

  /**
   * Handle animated input enter key press
   */
  onAnimatedInputEnter(value: string): void {
    if (value && value.length === 10) {
      this.autoNavigateToLoanDetails(value);
    }
  }

  /**
   * Auto-navigate to loan details when 10 digits are entered
   */
  private autoNavigateToLoanDetails(loanNumber: string): void {
    // Set searching state
    this.isSearching = true;
    this.searchError = null;

    // Find the loan (this will automatically add to recent searches)
    this.loanService.findLoanByNumber(loanNumber)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Auto-search failed:', error);
          this.searchError = 'Loan not found. Please check the loan number and try again.';
          this.isSearching = false;
          return of(null);
        })
      )
      .subscribe(loan => {
        this.isSearching = false;
        if (loan) {
          // Navigate to loan details page
          this.router.navigate(['/loan-details', loanNumber]);
        }
      });
  }

}
